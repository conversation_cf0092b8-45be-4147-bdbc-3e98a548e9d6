import pymongo
import asyncio
import logging
from typing import Optional, List, Dict, Any, Tuple
from pymongo import MongoClient
from datetime import datetime, timedelta, timezone
from bson import ObjectId

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self, mongo_url: str):
        self.mongo_url = mongo_url
        self.client = None
        self.db = None
        
    def connect(self):
        """Connect to MongoDB"""
        try:
            self.client = MongoClient(self.mongo_url)
            self.db = self.client['leakin']  # Use the leakin database
            # Test connection
            self.client.admin.command('ping')
            logger.info("Successfully connected to MongoDB")
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise e
    
    def disconnect(self):
        """Disconnect from MongoDB"""
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
    
    # License Key Operations
    def add_license_keys(self, keys: List[str]) -> int:
        """Add multiple license keys to the database"""
        collection = self.db['leakin-license-keys']
        
        key_docs = []
        for key in keys:
            # Check if key already exists
            if not collection.find_one({"key": key}):
                key_docs.append({
                    "key": key,
                    "redeemed": False,
                    "redeemed_by": None,
                    "redeemed_at": None,
                    "server_id": None,
                    "created_at": datetime.utcnow()
                })
        
        if key_docs:
            result = collection.insert_many(key_docs)
            logger.info(f"Added {len(result.inserted_ids)} new license keys")
            return len(result.inserted_ids)
        return 0
    
    def redeem_license_key(self, key: str, user_id: int, server_id: int) -> bool:
        """Redeem a license key for a user and server"""
        collection = self.db['leakin-license-keys']
        
        # Find unredeemed key
        key_doc = collection.find_one({"key": key, "redeemed": False})
        if not key_doc:
            return False
        
        # Update key as redeemed
        result = collection.update_one(
            {"_id": key_doc["_id"]},
            {
                "$set": {
                    "redeemed": True,
                    "redeemed_by": user_id,
                    "redeemed_at": datetime.utcnow(),
                    "server_id": server_id
                }
            }
        )
        
        return result.modified_count > 0
    
    def get_user_license_keys(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all license keys owned by a user"""
        collection = self.db['leakin-license-keys']
        return list(collection.find({"redeemed_by": user_id}))
    
    def get_server_license_key(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get the active license key for a server"""
        collection = self.db['leakin-license-keys']
        return collection.find_one({"server_id": server_id, "redeemed": True})
    
    def transfer_key_to_server(self, key: str, user_id: int, new_server_id: int) -> bool:
        """Transfer a license key to a different server with cleanup of old server's data"""
        collection = self.db['leakin-license-keys']
        
        # First get the current server_id to clean up
        current_key = collection.find_one({"key": key, "redeemed_by": user_id})
        if not current_key:
            return False
            
        old_server_id = current_key.get('server_id')
        
        # Update the server_id
        result = collection.update_one(
            {"key": key, "redeemed_by": user_id},
            {"$set": {"server_id": new_server_id}}
        )
        
        # If transfer was successful, clean up old server's role assignments
        if result.modified_count > 0 and old_server_id:
            # Clean up server config for old server
            config_collection = self.db['leakin-server-configs']
            config_collection.delete_one({"server_id": old_server_id})
            logger.info(f"Transferred key {key} from server {old_server_id} to {new_server_id}, cleaned up old config")
            
        return result.modified_count > 0
    
    def transfer_key_to_user(self, key: str, current_user_id: int, new_user_id: int) -> bool:
        """Transfer ownership of a license key to another user with cleanup"""
        collection = self.db['leakin-license-keys']
        
        # First get the server_id to clean up
        current_key = collection.find_one({"key": key, "redeemed_by": current_user_id})
        if not current_key:
            return False
            
        server_id = current_key.get('server_id')
        
        # Update the owner
        result = collection.update_one(
            {"key": key, "redeemed_by": current_user_id},
            {"$set": {"redeemed_by": new_user_id}}
        )
        
        # If transfer was successful, clean up server configuration
        if result.modified_count > 0 and server_id:
            # Clean up server config since the new owner will need to reconfigure
            config_collection = self.db['leakin-server-configs']
            config_collection.delete_one({"server_id": server_id})
            logger.info(f"Transferred key {key} from user {current_user_id} to {new_user_id} for server {server_id}, cleaned up server config")
            
        return result.modified_count > 0
    
    # Server Configuration Operations
    def save_server_config(self, server_id: int, config: Dict[str, Any]) -> bool:
        """Save server configuration"""
        collection = self.db['leakin-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {**config, "updated_at": datetime.utcnow()}},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def get_server_config(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get server configuration"""
        collection = self.db['leakin-server-configs']
        return collection.find_one({"server_id": server_id})
    
    def update_server_config_field(self, server_id: int, field: str, value: Any) -> bool:
        """Update a specific field in server configuration"""
        collection = self.db['leakin-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {field: value, "updated_at": datetime.utcnow()}},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def add_ignored_user(self, server_id: int, user_id: int) -> bool:
        """Add a user to the ignored list for a server"""
        collection = self.db['leakin-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$addToSet": {"ignored_users": user_id}},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def remove_ignored_user(self, server_id: int, user_id: int) -> bool:
        """Remove a user from the ignored list for a server"""
        collection = self.db['leakin-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$pull": {"ignored_users": user_id}}
        )
        
        return result.modified_count > 0
    
    def is_server_licensed(self, server_id: int) -> bool:
        """Check if a server has a valid license"""
        license_key = self.get_server_license_key(server_id)
        return license_key is not None
    
    def is_server_configured(self, server_id: int) -> tuple[bool, List[str]]:
        """Check if server is properly configured"""
        config = self.get_server_config(server_id)
        if not config:
            return False, ["No configuration found"]
        
        required_fields = ['role_id', 'channel_id', 'trigger_word']
        missing_fields = []
        
        for field in required_fields:
            if field not in config or config[field] is None:
                missing_fields.append(field)
        
        return len(missing_fields) == 0, missing_fields
        
    # Gender Verification Methods
    def set_gender_verification_settings(self, server_id: int, channel_id: int, category_id: int, 
                                       support_role_id: int, paper_text: str) -> bool:
        """Save gender verification settings for a server"""
        collection = self.db['leakin-gender-verification']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "channel_id": channel_id,
                "category_id": category_id,
                "support_role_id": support_role_id,
                "paper_text": paper_text,
                "updated_at": datetime.utcnow()
            }},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def get_gender_verification_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get gender verification settings for a server"""
        collection = self.db['leakin-gender-verification']
        return collection.find_one({"server_id": server_id})
    
    def create_gender_verification_ticket(self, server_id: int, user_id: int) -> str:
        """Create a new gender verification ticket"""
        collection = self.db['leakin-gender-tickets']
        
        # Check if user already has an open ticket
        existing_ticket = collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "open"
        })
        
        if existing_ticket:
            return "existing"
            
        # Check if user had a ticket closed in the last 12 hours
        recent_ticket = collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "closed",
            "closed_at": {"$gt": datetime.utcnow() - timedelta(hours=12)}
        })
        
        if recent_ticket:
            return "recent"
            
        # Create new ticket
        ticket = {
            "server_id": server_id,
            "user_id": user_id,
            "channel_id": None,
            "status": "open",
            "created_at": datetime.utcnow(),
            "closed_at": None
        }
        
        result = collection.insert_one(ticket)
        return str(result.inserted_id)
    
    def close_gender_verification_ticket(self, ticket_id: str, channel_id: int) -> bool:
        """Close a gender verification ticket"""
        collection = self.db['leakin-gender-tickets']
        
        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {"$set": {
                "status": "closed",
                "closed_at": datetime.utcnow(),
                "channel_id": channel_id
            }}
        )
        
        return result.modified_count > 0
    
    def get_user_open_ticket(self, server_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user's open gender verification ticket"""
        collection = self.db['leakin-gender-tickets']
        return collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "open"
        })
    
    def cleanup_old_tickets(self):
        """Clean up tickets that have been open for more than 24 hours"""
        try:
            # Check if database is initialized by trying to access a collection
            try:
                collection = self.db.get_collection('leakin-gender-tickets')
            except Exception as e:
                logger.error(f"Database error: {e}")
                return 0
            
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
            
            result = collection.update_many(
                {
                    "status": "open",
                    "created_at": {"$lt": cutoff_time}
                },
                {"$set": {"status": "closed", "closed_at": datetime.now(timezone.utc)}}
            )
            
            logger.info(f"Cleaned up {result.modified_count} old tickets")
            return result.modified_count
            
        except Exception as e:
            logger.error(f"Error in cleanup_old_tickets: {e}", exc_info=True)
            return 0
